server:
  port: 3333
  address: 0.0.0.0

spring:
  application:
    name: document-extraction-api
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      enabled: true
  web:
    cors:
      allowed-origins: "*"
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: false

# AWS Configuration
aws:
  access-key-id: ********************
  secret-access-key: kzLpPpgc9nX0nOCGFf5xcBxlBz+PH/66Hyd0BmU8
  region: ap-south-1
  s3:
    bucket-name: doc-scan-textract
  textract:
    enabled: true
    timeout: 30000

# Upload Configuration
upload:
  max-file-size: 52428800
  allowed-extensions:
    - jpg
    - jpeg
    - png
    - pdf
  upload-directory: uploads

# Logging Configuration
logging:
  level:
    com.textract: INFO
    software.amazon.awssdk: WARN
    org.springframework: INFO
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/api.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
