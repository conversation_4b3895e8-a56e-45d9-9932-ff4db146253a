server:
  port: 8081
  address: 0.0.0.0

spring:
  application:
    name: document-extraction-ui
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      enabled: true
  web:
    cors:
      allowed-origins: "*"
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: false
  resources:
    static-locations: classpath:/static/
    cache:
      cachecontrol:
        max-age: 0
        no-cache: true

# API Configuration
api:
  host: localhost
  port: 3333
  base-url: http://localhost:3333

# Logging Configuration
logging:
  level:
    com.textract: INFO
    org.springframework: INFO
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ui.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
