package com.textract.api.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.textract.api.config.ApplicationProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Document processor for handling file uploads and OCR extraction
 */
@Service
public class DocumentProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentProcessor.class);
    
    @Autowired
    private TextractService textractService;
    
    @Autowired
    private ApplicationProperties applicationProperties;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // In-memory storage for processed documents
    private final Map<String, Map<String, Object>> processedDocuments = new ConcurrentHashMap<>();
    private final List<Map<String, Object>> extractedDocuments = new ArrayList<>();
    
    // Supported file types
    private static final Set<String> SUPPORTED_EXTENSIONS = Set.of(
        ".jpg", ".jpeg", ".png", ".pdf"
    );

    /**
     * Process uploaded files
     */
    public Map<String, Object> processUploadedFiles(List<MultipartFile> files) {
        logger.info("Processing {} uploaded files", files.size());
        
        List<Map<String, Object>> extractedDocuments = new ArrayList<>();
        List<Map<String, Object>> errors = new ArrayList<>();
        int successfullyProcessed = 0;
        
        for (MultipartFile file : files) {
            try {
                Map<String, Object> result = processUploadedFile(file);
                if (result != null) {
                    extractedDocuments.add(result);
                    successfullyProcessed++;
                    
                    // Store in memory
                    String documentId = UUID.randomUUID().toString();
                    result.put("document_id", documentId);
                    processedDocuments.put(documentId, result);
                    this.extractedDocuments.add(result);
                }
            } catch (Exception e) {
                logger.error("Error processing file {}: {}", file.getOriginalFilename(), e.getMessage());
                Map<String, Object> error = new HashMap<>();
                error.put("filename", file.getOriginalFilename());
                error.put("error", e.getMessage());
                errors.add(error);
            }
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("total_files_uploaded", files.size());
        response.put("successfully_processed", successfullyProcessed);
        response.put("errors", errors);
        response.put("extracted_documents", extractedDocuments);
        response.put("processing_timestamp", Instant.now().toString());
        
        return response;
    }

    /**
     * Process a single uploaded file
     */
    private Map<String, Object> processUploadedFile(MultipartFile file) throws IOException {
        String filename = file.getOriginalFilename();
        
        logger.info("Processing file: {}", filename);
        
        // Validate file type
        if (!isValidFileType(filename)) {
            throw new IllegalArgumentException("Unsupported file type: " + filename);
        }
        
        // Read file content
        byte[] fileContent = file.getBytes();

        // Upload to S3 and get the key for processing
        String s3Key = textractService.uploadToS3(fileContent, filename);
        boolean isPdf = filename.toLowerCase().endsWith(".pdf");

        if (isPdf && s3Key == null) {
            throw new RuntimeException("PDF processing requires S3 configuration. Please ensure AWS S3 credentials and bucket are properly configured.");
        }

        if (s3Key != null) {
            logger.info("✅ File uploaded to S3 with key: {}", s3Key);
        }

        // Process with AWS Textract only - no mock data
        Map<String, Object> textractResult;
        try {
            textractResult = textractService.processDocument(fileContent, filename, s3Key);
        } catch (RuntimeException e) {
            // AWS Textract is not available - throw exception to be caught by caller
            throw new RuntimeException("AWS Textract service unavailable: " + e.getMessage(), e);
        }

        // Extract the fields from the Textract result
        @SuppressWarnings("unchecked")
        Map<String, Object> extractedFields = (Map<String, Object>) textractResult.getOrDefault("extracted_fields", new HashMap<>());

        // Create the final document structure
        Map<String, Object> extractedData = new HashMap<>();
        extractedData.putAll(extractedFields); // Copy all extracted fields

        // Add metadata
        extractedData.put("filename", filename);
        extractedData.put("file_size", fileContent.length);
        extractedData.put("processed_at", Instant.now().toString());
        extractedData.put("raw_text", textractResult.getOrDefault("raw_text", ""));
        extractedData.put("processing_method", textractResult.getOrDefault("processing_method", "UNKNOWN"));

        // Add S3 information if uploaded
        if (s3Key != null) {
            extractedData.put("s3_key", s3Key);
            extractedData.put("s3_bucket", textractService.getS3BucketName());
            extractedData.put("s3_url", "s3://" + textractService.getS3BucketName() + "/" + s3Key);
        }

        return extractedData;
    }

    /**
     * Check if file type is supported
     */
    private boolean isValidFileType(String filename) {
        String extension = filename.toLowerCase();
        return SUPPORTED_EXTENSIONS.stream()
            .anyMatch(extension::endsWith);
    }

    /**
     * Create grouped response with person-based grouping
     */
    public Map<String, Object> createGroupedResponse() {
        Map<String, List<Map<String, Object>>> groupedByPerson = groupDocumentsByPerson();
        
        List<Map<String, Object>> groupedArray = new ArrayList<>();
        int groupId = 1;
        
        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedByPerson.entrySet()) {
            String personName = entry.getKey();
            List<Map<String, Object>> documents = entry.getValue();
            
            Map<String, Object> group = new HashMap<>();
            group.put("group_id", groupId++);
            group.put("person_name", personName);
            group.put("total_documents", documents.size());
            group.put("documents", documents);
            
            groupedArray.add(group);
        }
        
        Map<String, Object> personalDocuments = new HashMap<>();
        personalDocuments.put("count", extractedDocuments.size());
        personalDocuments.put("grouped_by_person", groupedArray);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("total_files", extractedDocuments.size());
        response.put("processed_files", extractedDocuments.size());
        response.put("personal_documents", personalDocuments);
        response.put("timestamp", Instant.now().toString());
        
        return response;
    }

    /**
     * Group documents by person name
     */
    private Map<String, List<Map<String, Object>>> groupDocumentsByPerson() {
        Map<String, List<Map<String, Object>>> grouped = new HashMap<>();
        
        for (Map<String, Object> doc : extractedDocuments) {
            String personName = getPersonName(doc);
            grouped.computeIfAbsent(personName, k -> new ArrayList<>()).add(doc);
        }
        
        return grouped;
    }

    /**
     * Extract person name from document
     */
    private String getPersonName(Map<String, Object> document) {
        String firstName = (String) document.getOrDefault("FIRST_NAME", "");
        String middleName = (String) document.getOrDefault("MIDDLE_NAME", "");
        String lastName = (String) document.getOrDefault("LAST_NAME", "");
        
        List<String> nameParts = Arrays.asList(firstName, middleName, lastName)
            .stream()
            .filter(part -> part != null && !part.trim().isEmpty())
            .collect(Collectors.toList());
        
        return nameParts.isEmpty() ? "Unknown Person" : String.join(" ", nameParts);
    }

    /**
     * Get AWS status from TextractService
     */
    public Map<String, Object> getAwsStatus() {
        return textractService.getAwsStatus();
    }
}
