package com.textract.api;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * Main Spring Boot Application for AWS Textract Document Extraction API
 * 
 * This application provides REST endpoints for document processing
 * using AWS Textract OCR technology.
 */
@SpringBootApplication
@EnableConfigurationProperties
public class DocumentExtractionApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(DocumentExtractionApiApplication.class, args);
    }
}
