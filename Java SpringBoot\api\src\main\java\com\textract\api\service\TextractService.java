package com.textract.api.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.textract.api.config.ApplicationProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.textract.TextractClient;
import software.amazon.awssdk.services.textract.model.*;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Service for AWS Textract integration and document processing
 */
@Service
public class TextractService {
    
    private static final Logger logger = LoggerFactory.getLogger(TextractService.class);
    
    @Autowired
    private TextractClient textractClient;
    
    @Autowired
    private S3Client s3Client;
    
    @Autowired
    private ApplicationProperties applicationProperties;
    
    // Document type patterns
    private static final Map<String, Pattern> DOCUMENT_PATTERNS = Map.of(
        "AADHAAR", Pattern.compile("(?i)(aadhaar|unique identification|uid)"),
        "PAN", Pattern.compile("(?i)(permanent account number|pan|income tax)"),
        "PASSPORT", Pattern.compile("(?i)(passport|republic of india)"),
        "DRIVING_LICENSE", Pattern.compile("(?i)(driving licence|driving license|dl)"),
        "VOTER_ID", Pattern.compile("(?i)(voter|election|electoral)"),
        "GST_CERTIFICATE", Pattern.compile("(?i)(gst|goods and services tax|gstin|tax certificate)")
    );

    // Field extraction patterns - improved for OCR text
    private static final Map<String, Pattern> FIELD_PATTERNS = Map.of(
        "PAN_NUMBER", Pattern.compile("[A-Z]{5}\\d{4}[A-Z]"),
        "AADHAAR_NUMBER", Pattern.compile("\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b"),
        "PASSPORT_NUMBER", Pattern.compile("[A-Z]\\d{7}"),
        "DATE_PATTERN", Pattern.compile("\\b\\d{1,2}[/\\-]\\d{1,2}[/\\-]\\d{4}\\b"),
        // Improved name pattern for Aadhaar - looks for lines with proper names after cleaning
        "AADHAAR_NAME_PATTERN", Pattern.compile("(?i)(?:^|\\n)([A-Z][a-z]+(?:\\s+[A-Z][a-z]+){1,3})(?=\\s*\\n)", Pattern.MULTILINE),
        // DOB pattern that looks for DOB: followed by date
        "DOB_PATTERN", Pattern.compile("(?i)(?:date of birth|dob)[:\\s/]*([0-9]{1,2}[/\\-][0-9]{1,2}[/\\-][0-9]{4})"),
        // Gender pattern
        "GENDER_PATTERN", Pattern.compile("(?i)\\b(MALE|FEMALE|M|F)\\b"),
        "ADDRESS_PATTERN", Pattern.compile("(?:ADDRESS|ADDR)[:\\s]*\\n([^\\n]+(?:\\n[^\\n]+)*?)(?:\\n\\n|$)", Pattern.CASE_INSENSITIVE)
    );

    /**
     * Process document using AWS Textract only - no mock data
     */
    public Map<String, Object> processDocument(byte[] documentBytes, String filename) {
        return processDocument(documentBytes, filename, null);
    }

    /**
     * Process document using AWS Textract with optional S3 key
     */
    public Map<String, Object> processDocument(byte[] documentBytes, String filename, String s3Key) {
        if (isAwsTextractAvailable()) {
            logger.info("🔄 Processing document with AWS Textract: {}", filename);
            return processWithTextract(documentBytes, filename, s3Key);
        } else {
            logger.error("ERROR: AWS Textract not available - cannot process document: {}", filename);
            logger.error("ERROR: Reason: AWS credentials not configured or connection failed");
            throw new RuntimeException("AWS Textract service is not available. Please check AWS credentials and configuration.");
        }
    }

    /**
     * Check if AWS Textract is available
     */
    public boolean isAwsTextractAvailable() {
        return textractClient != null && applicationProperties.getAws().getTextract().isEnabled();
    }

    /**
     * Upload file to S3 bucket
     */
    public String uploadToS3(byte[] fileBytes, String filename) {
        if (s3Client == null || applicationProperties.getAws().getS3().getBucketName() == null) {
            logger.warn("S3 client not initialized, cannot upload file: {}", filename);
            return null;
        }

        try {
            // Generate unique key for S3 object
            String timestamp = String.valueOf(System.currentTimeMillis());
            String s3Key = "documents/" + timestamp + "_" + filename;

            // Create put object request
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(applicationProperties.getAws().getS3().getBucketName())
                .key(s3Key)
                .contentType(getContentType(filename))
                .build();

            // Upload file to S3
            PutObjectResponse response = s3Client.putObject(putObjectRequest,
                software.amazon.awssdk.core.sync.RequestBody.fromBytes(fileBytes));

            logger.info("SUCCESS: File uploaded to S3: s3://{}/{}", 
                applicationProperties.getAws().getS3().getBucketName(), s3Key);
            return s3Key;

        } catch (Exception e) {
            logger.error("ERROR: Failed to upload file to S3: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Get S3 bucket name
     */
    public String getS3BucketName() {
        return applicationProperties.getAws().getS3().getBucketName();
    }

    /**
     * Get AWS connection status
     */
    public Map<String, Object> getAwsStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("textract_available", isAwsTextractAvailable());
        status.put("s3_bucket", applicationProperties.getAws().getS3().getBucketName());
        status.put("textract_client_initialized", textractClient != null);
        status.put("s3_client_initialized", s3Client != null);
        return status;
    }

    /**
     * Get content type based on file extension
     */
    private String getContentType(String filename) {
        String lowerFilename = filename.toLowerCase();
        if (lowerFilename.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFilename.endsWith(".jpg") || lowerFilename.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFilename.endsWith(".png")) {
            return "image/png";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * Process document using AWS Textract
     */
    private Map<String, Object> processWithTextract(byte[] documentBytes, String filename, String existingS3Key) {
        try {
            logger.info("Processing document with AWS Textract: {}", filename);

            // Check if it's a PDF file
            boolean isPdf = filename.toLowerCase().endsWith(".pdf");
            Document document;
            String s3Key = existingS3Key;

            if (isPdf) {
                logger.info("PDF detected, uploading to S3 for processing: {}", filename);
                validatePdfFile(documentBytes, filename);

                if (s3Key == null) {
                    s3Key = uploadToS3(documentBytes, filename);
                    if (s3Key == null) {
                        throw new RuntimeException("Failed to upload PDF to S3 for processing. S3 configuration may be missing or invalid.");
                    }
                }

                // For PDFs, use asynchronous processing (required by AWS Textract)
                logger.info("PDF detected - using asynchronous Textract processing for: {}", filename);
                return processWithAsyncTextract(s3Key, filename);

            } else {
                // For images, validate size and use bytes directly
                validateImageFile(documentBytes, filename);

                document = Document.builder()
                    .bytes(SdkBytes.fromByteArray(documentBytes))
                    .build();

                logger.info("Using direct bytes for image processing: {}", filename);
            }

            // Try FORMS analysis first
            Map<String, Object> result = null;
            try {
                result = analyzeDocumentWithForms(document, filename);

                // If no key-value pairs found, try basic text detection
                if (((Map<String, Object>) result.get("extracted_fields")).isEmpty()) {
                    logger.info("No forms detected, trying text detection for: {}", filename);
                    result = analyzeDocumentWithTextDetection(document, filename);
                }
            } catch (Exception formsError) {
                logger.warn("Forms analysis failed ({}), falling back to text detection for: {}", formsError.getMessage(), filename);

                // Try basic text detection as fallback
                try {
                    result = analyzeDocumentWithTextDetection(document, filename);
                    logger.info("Text detection succeeded as fallback for: {}", filename);
                } catch (Exception textError) {
                    logger.error("Both forms analysis and text detection failed for: {}", filename);
                    throw new RuntimeException("All Textract processing methods failed. Forms error: " + formsError.getMessage() + ". Text detection error: " + textError.getMessage());
                }
            }

            return result;

        } catch (Exception e) {
            logger.error("Error processing document with Textract: {}", e.getMessage());
            throw new RuntimeException("AWS Textract processing failed: " + e.getMessage(), e);
        }
    }

    /**
     * Analyze document using FORMS feature
     */
    private Map<String, Object> analyzeDocumentWithForms(Document document, String filename) throws Exception {
        if (filename.toLowerCase().endsWith(".pdf")) {
            throw new RuntimeException("ERROR: PDF files should use async processing, not sync analyzeDocumentWithForms!");
        }

        AnalyzeDocumentRequest request = AnalyzeDocumentRequest.builder()
            .document(document)
            .featureTypes(FeatureType.FORMS, FeatureType.TABLES)
            .build();

        AnalyzeDocumentResponse response = textractClient.analyzeDocument(request);

        return extractDataFromResponse(response.blocks(), filename);
    }

    /**
     * Analyze document using basic text detection
     */
    private Map<String, Object> analyzeDocumentWithTextDetection(Document document, String filename) throws Exception {
        if (filename.toLowerCase().endsWith(".pdf")) {
            throw new RuntimeException("ERROR: PDF files should use async processing, not sync detectDocumentText!");
        }

        DetectDocumentTextRequest request = DetectDocumentTextRequest.builder()
            .document(document)
            .build();

        DetectDocumentTextResponse response = textractClient.detectDocumentText(request);

        return extractDataFromTextBlocks(response.blocks(), filename);
    }

    /**
     * Extract structured data from Textract response blocks
     */
    private Map<String, Object> extractDataFromResponse(List<Block> blocks, String filename) {
        Map<String, String> keyValuePairs = new HashMap<>();
        StringBuilder allText = new StringBuilder();

        // Extract key-value pairs and all text
        Map<String, Block> blockMap = blocks.stream()
            .collect(Collectors.toMap(Block::id, block -> block));

        for (Block block : blocks) {
            if (block.blockType() == BlockType.KEY_VALUE_SET) {
                if (block.entityTypes().contains(EntityType.KEY)) {
                    String key = getBlockText(block, blockMap).trim();
                    String value = "";

                    if (block.relationships() != null) {
                        for (Relationship relationship : block.relationships()) {
                            if (relationship.type() == RelationshipType.VALUE) {
                                for (String valueId : relationship.ids()) {
                                    Block valueBlock = blockMap.get(valueId);
                                    if (valueBlock != null) {
                                        value = getBlockText(valueBlock, blockMap).trim();
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    if (!key.isEmpty() && !value.isEmpty()) {
                        keyValuePairs.put(key.toUpperCase(), value);
                    }
                }
            } else if (block.blockType() == BlockType.LINE) {
                allText.append(block.text()).append("\n");
            }
        }

        // Map to standard fields
        Map<String, Object> extractedFields = mapToStandardFields(keyValuePairs, allText.toString(), filename);

        Map<String, Object> result = new HashMap<>();
        result.put("extracted_fields", extractedFields);
        result.put("raw_text", allText.toString());
        result.put("processing_method", "AWS_TEXTRACT_FORMS");
        return result;
    }

    /**
     * Extract data from text-only blocks
     */
    private Map<String, Object> extractDataFromTextBlocks(List<Block> blocks, String filename) {
        StringBuilder allText = new StringBuilder();

        for (Block block : blocks) {
            if (block.blockType() == BlockType.LINE) {
                allText.append(block.text()).append("\n");
            }
        }

        String text = allText.toString();
        Map<String, Object> extractedFields = extractFromPlainText(text, filename);

        Map<String, Object> result = new HashMap<>();
        result.put("extracted_fields", extractedFields);
        result.put("raw_text", text);
        result.put("processing_method", "AWS_TEXTRACT_TEXT_DETECTION");
        return result;
    }

    // Additional helper methods would continue here...
    // Due to length constraints, I'll create separate files for the remaining methods
}
