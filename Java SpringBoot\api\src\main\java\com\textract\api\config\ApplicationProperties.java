package com.textract.api.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Configuration properties for the application
 */
@Component
@ConfigurationProperties(prefix = "")
public class ApplicationProperties {

    private Aws aws = new Aws();
    private Upload upload = new Upload();

    public Aws getAws() {
        return aws;
    }

    public void setAws(Aws aws) {
        this.aws = aws;
    }

    public Upload getUpload() {
        return upload;
    }

    public void setUpload(Upload upload) {
        this.upload = upload;
    }

    public static class Aws {
        private String accessKeyId;
        private String secretAccessKey;
        private String region;
        private S3 s3 = new S3();
        private Textract textract = new Textract();

        public String getAccessKeyId() {
            return accessKeyId;
        }

        public void setAccessKeyId(String accessKeyId) {
            this.accessKeyId = accessKeyId;
        }

        public String getSecretAccessKey() {
            return secretAccessKey;
        }

        public void setSecretAccessKey(String secretAccessKey) {
            this.secretAccessKey = secretAccessKey;
        }

        public String getRegion() {
            return region;
        }

        public void setRegion(String region) {
            this.region = region;
        }

        public S3 getS3() {
            return s3;
        }

        public void setS3(S3 s3) {
            this.s3 = s3;
        }

        public Textract getTextract() {
            return textract;
        }

        public void setTextract(Textract textract) {
            this.textract = textract;
        }

        public static class S3 {
            private String bucketName;

            public String getBucketName() {
                return bucketName;
            }

            public void setBucketName(String bucketName) {
                this.bucketName = bucketName;
            }
        }

        public static class Textract {
            private boolean enabled;
            private int timeout;

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public int getTimeout() {
                return timeout;
            }

            public void setTimeout(int timeout) {
                this.timeout = timeout;
            }
        }
    }

    public static class Upload {
        private long maxFileSize;
        private List<String> allowedExtensions;
        private String uploadDirectory;

        public long getMaxFileSize() {
            return maxFileSize;
        }

        public void setMaxFileSize(long maxFileSize) {
            this.maxFileSize = maxFileSize;
        }

        public List<String> getAllowedExtensions() {
            return allowedExtensions;
        }

        public void setAllowedExtensions(List<String> allowedExtensions) {
            this.allowedExtensions = allowedExtensions;
        }

        public String getUploadDirectory() {
            return uploadDirectory;
        }

        public void setUploadDirectory(String uploadDirectory) {
            this.uploadDirectory = uploadDirectory;
        }
    }
}
