package com.textract.ui.controller;

import com.textract.ui.config.UIProperties;
import com.textract.ui.service.ApiProxyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller for UI endpoints and API proxy
 */
@RestController
@CrossOrigin(origins = "*")
public class UIController {

    private static final Logger logger = LoggerFactory.getLogger(UIController.class);

    @Autowired
    private ApiProxyService apiProxyService;

    @Autowired
    private UIProperties uiProperties;

    /**
     * UI Health check endpoint
     */
    @GetMapping("/ui/health")
    public ResponseEntity<Map<String, Object>> uiHealth() {
        logger.debug("UI health check requested");
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "OK");
        response.put("message", "Document Extraction UI is running");
        response.put("timestamp", System.currentTimeMillis());
        response.put("version", "1.0.0");
        response.put("api_url", uiProperties.getApi().getBaseUrl());
        
        return ResponseEntity.ok(response);
    }

    /**
     * UI configuration endpoint
     */
    @GetMapping("/ui/config")
    public ResponseEntity<Map<String, Object>> uiConfig() {
        Map<String, Object> features = new HashMap<>();
        features.put("fileUpload", true);
        features.put("documentGrouping", true);
        features.put("richView", true);
        features.put("jsonView", true);
        
        Map<String, Object> config = new HashMap<>();
        config.put("apiUrl", uiProperties.getApi().getBaseUrl());
        config.put("version", "1.0.0");
        config.put("features", features);
        config.put("supportedFormats", new String[]{"JPG", "JPEG", "PNG", "PDF"});
        config.put("maxFileSize", "50MB");
        
        return ResponseEntity.ok(config);
    }

    /**
     * Proxy upload requests to API server
     */
    @PostMapping("/upload")
    public Mono<ResponseEntity<String>> uploadFiles(@RequestParam("files") List<MultipartFile> files) {
        logger.info("Proxying upload request to API server for {} files", files.size());

        if (files.isEmpty()) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "No files uploaded");
            return Mono.just(ResponseEntity.badRequest()
                .contentType(MediaType.APPLICATION_JSON)
                .body(error.toString()));
        }

        return apiProxyService.proxyUpload(files)
                .map(response -> ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response))
                .onErrorReturn(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("{\"success\":false,\"error\":\"Failed to connect to API server\"}"));
    }

    /**
     * Proxy API requests to API server
     */
    @GetMapping("/api/**")
    public Mono<ResponseEntity<String>> proxyApiGet(@RequestParam Map<String, String> params,
                                                   @RequestHeader Map<String, String> headers) {
        String path = extractApiPath();
        logger.debug("Proxying API GET request: {}", path);
        
        return apiProxyService.proxyGet(path)
                .map(response -> ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response))
                .onErrorReturn(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("{\"success\":false,\"error\":\"Failed to connect to API server\"}"));
    }

    /**
     * Proxy POST API requests to API server
     */
    @PostMapping("/api/**")
    public Mono<ResponseEntity<String>> proxyApiPost(@RequestBody(required = false) Object body) {
        String path = extractApiPath();
        logger.debug("Proxying API POST request: {}", path);
        
        return apiProxyService.proxyPost(path, body)
                .map(response -> ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response))
                .onErrorReturn(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("{\"success\":false,\"error\":\"Failed to connect to API server\"}"));
    }

    /**
     * Proxy health check to API server
     */
    @GetMapping("/health")
    public Mono<ResponseEntity<String>> proxyHealth() {
        logger.debug("Proxying health check to API server");
        
        return apiProxyService.proxyGet("/health")
                .map(response -> ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response))
                .onErrorReturn(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("{\"status\":\"ERROR\",\"message\":\"API server unavailable\"}"));
    }

    /**
     * Extract API path from request
     */
    private String extractApiPath() {
        // This is a simplified implementation
        // In a real application, you would extract the actual path from the request
        return "/api/grouped-by-person"; // Default for now
    }
}
