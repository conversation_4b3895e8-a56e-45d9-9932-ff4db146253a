package com.textract.api.controller;

import com.textract.api.service.DocumentProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST Controller for document processing endpoints
 */
@RestController
@CrossOrigin(origins = "*")
public class DocumentController {

    private static final Logger logger = LoggerFactory.getLogger(DocumentController.class);

    @Autowired
    private DocumentProcessor documentProcessor;

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        logger.debug("Health check requested");
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "OK");
        response.put("message", "Document Extraction API is running");
        response.put("timestamp", System.currentTimeMillis());
        response.put("version", "1.0.0");
        
        return ResponseEntity.ok(response);
    }

    /**
     * AWS status endpoint
     */
    @GetMapping("/aws-status")
    public ResponseEntity<Map<String, Object>> awsStatus() {
        logger.debug("AWS status check requested");
        Map<String, Object> awsStatus = documentProcessor.getAwsStatus();
        return ResponseEntity.ok(awsStatus);
    }

    /**
     * Document upload endpoint
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadDocuments(
            @RequestParam("files") List<MultipartFile> files) {
        try {
            logger.info("Document upload requested for {} files", files.size());
            
            if (files.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(createErrorResponse("No files uploaded"));
            }
            
            Map<String, Object> result = documentProcessor.processUploadedFiles(files);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("Error handling upload", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(createErrorResponse("Error processing upload: " + e.getMessage()));
        }
    }

    /**
     * Get grouped documents endpoint
     */
    @GetMapping("/api/grouped-by-person")
    public ResponseEntity<Map<String, Object>> getGroupedDocuments() {
        try {
            logger.info("Grouped documents requested");
            Map<String, Object> response = documentProcessor.createGroupedResponse();
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error getting grouped documents", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(createErrorResponse("Error retrieving grouped documents: " + e.getMessage()));
        }
    }

    /**
     * Process personal documents endpoint
     */
    @PostMapping("/api/process-personal-documents")
    public ResponseEntity<Map<String, Object>> processPersonalDocuments() {
        try {
            logger.info("Process personal documents requested");
            Map<String, Object> response = documentProcessor.createGroupedResponse();
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error processing personal documents", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(createErrorResponse("Error processing personal documents: " + e.getMessage()));
        }
    }

    /**
     * API info endpoint
     */
    @GetMapping("/api/info")
    public ResponseEntity<Map<String, Object>> apiInfo() {
        Map<String, Object> endpoints = new HashMap<>();
        endpoints.put("upload", "POST /upload");
        endpoints.put("grouped", "GET /api/grouped-by-person");
        endpoints.put("process", "POST /api/process-personal-documents");
        endpoints.put("health", "GET /health");
        
        Map<String, Object> response = new HashMap<>();
        response.put("name", "AWS Textract Document Extraction API");
        response.put("version", "1.0.0");
        response.put("description", "Spring Boot API for document processing using AWS Textract");
        response.put("endpoints", endpoints);
        
        return ResponseEntity.ok(response);
    }

    /**
     * Create error response
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> error = new HashMap<>();
        error.put("success", false);
        error.put("error", message);
        error.put("timestamp", Instant.now().toString());
        return error;
    }
}
