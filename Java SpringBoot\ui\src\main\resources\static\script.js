class DocumentExtractor {
    constructor() {
        this.selectedFiles = [];
        this.results = null;
        this.apiBaseUrl = 'http://localhost:8081'; // Java Vert.x UI server URL
        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.fileList = document.getElementById('fileList');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.resultsSection = document.getElementById('resultsSection');
        this.errorSection = document.getElementById('errorSection');

        // View elements
        this.richView = document.getElementById('richView');
        this.jsonView = document.getElementById('jsonView');
        this.richViewBtn = document.getElementById('richViewBtn');
        this.jsonViewBtn = document.getElementById('jsonViewBtn');

        // Control buttons
        this.copyJsonBtn = document.getElementById('copyJsonBtn');
        this.formatJsonBtn = document.getElementById('formatJsonBtn');
        this.downloadJsonBtn = document.getElementById('downloadJsonBtn');
        this.exportRichBtn = document.getElementById('exportRichBtn');
        this.printRichBtn = document.getElementById('printRichBtn');
    }

    bindEvents() {
        // File input events
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // Drag and drop events
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        
        // Button events
        this.uploadBtn.addEventListener('click', () => this.uploadFiles());
        this.clearBtn.addEventListener('click', () => this.clearFiles());

        // View toggle events
        this.richViewBtn.addEventListener('click', () => this.switchToRichView());
        this.jsonViewBtn.addEventListener('click', () => this.switchToJsonView());

        // Control button events
        this.copyJsonBtn.addEventListener('click', () => this.copyJson());
        this.formatJsonBtn.addEventListener('click', () => this.formatJson());
        this.downloadJsonBtn.addEventListener('click', () => this.downloadResults());
        this.exportRichBtn.addEventListener('click', () => this.exportRichData());
        this.printRichBtn.addEventListener('click', () => this.printRichView());
    }

    handleFileSelect(event) {
        const files = Array.from(event.target.files);
        this.addFiles(files);
    }

    handleDragOver(event) {
        event.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
        const files = Array.from(event.dataTransfer.files);
        this.addFiles(files);
    }

    addFiles(files) {
        const validFiles = files.filter(file => {
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
            return validTypes.includes(file.type);
        });

        if (validFiles.length !== files.length) {
            this.showError('Some files were skipped. Only JPG, JPEG, PNG, and PDF files are supported.');
        }

        this.selectedFiles = [...this.selectedFiles, ...validFiles];
        this.updateFileList();
        this.updateUploadButton();
        this.hideError();
    }

    updateFileList() {
        this.fileList.innerHTML = '';
        
        this.selectedFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            
            const fileInfo = document.createElement('div');
            fileInfo.className = 'file-info';
            
            const fileIcon = document.createElement('span');
            fileIcon.className = 'file-icon';
            fileIcon.textContent = this.getFileIcon(file.type);
            
            const fileName = document.createElement('span');
            fileName.className = 'file-name';
            fileName.textContent = file.name;
            
            const fileSize = document.createElement('span');
            fileSize.className = 'file-size';
            fileSize.textContent = this.formatFileSize(file.size);
            
            const removeBtn = document.createElement('button');
            removeBtn.className = 'remove-file';
            removeBtn.textContent = '×';
            removeBtn.onclick = () => this.removeFile(index);
            
            fileInfo.appendChild(fileIcon);
            fileInfo.appendChild(fileName);
            fileInfo.appendChild(fileSize);
            
            fileItem.appendChild(fileInfo);
            fileItem.appendChild(removeBtn);
            
            this.fileList.appendChild(fileItem);
        });
    }

    getFileIcon(type) {
        if (type.includes('pdf')) return '📄';
        if (type.includes('image')) return '🖼️';
        return '📁';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    removeFile(index) {
        this.selectedFiles.splice(index, 1);
        this.updateFileList();
        this.updateUploadButton();
    }

    updateUploadButton() {
        this.uploadBtn.disabled = this.selectedFiles.length === 0;
    }

    clearFiles() {
        this.selectedFiles = [];
        this.fileInput.value = '';
        this.updateFileList();
        this.updateUploadButton();
        this.hideResults();
    }

    async uploadFiles() {
        if (this.selectedFiles.length === 0) return;

        this.showLoading(true);
        this.hideResults();
        this.hideError();

        const formData = new FormData();
        this.selectedFiles.forEach(file => {
            formData.append('documents', file);
        });

        try {
            // Upload to API server
            const response = await fetch(`${this.apiBaseUrl}/upload`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok) {
                // After upload, fetch grouped results from API server
                const groupRes = await fetch(`${this.apiBaseUrl}/api/grouped-by-person`);
                const groupData = await groupRes.json();
                this.results = groupData.personal_documents.grouped_by_person;
                this.displayResults();
            } else {
                this.showError(result.error || 'Upload failed');
            }
        } catch (error) {
            this.showError('Network error: ' + error.message + '. Make sure the API server is running on ' + this.apiBaseUrl);
        } finally {
            this.showLoading(false);
        }
    }

    showLoading(show) {
        const btnText = this.uploadBtn.querySelector('.btn-text');
        const loader = this.uploadBtn.querySelector('.loader');
        
        if (show) {
            btnText.style.display = 'none';
            loader.style.display = 'block';
            this.uploadBtn.disabled = true;
        } else {
            btnText.style.display = 'block';
            loader.style.display = 'none';
            this.uploadBtn.disabled = this.selectedFiles.length === 0;
        }
    }

    displayResults() {
        if (!this.results) return;

        this.showRichResults();
        this.showJsonResults();
        this.resultsSection.style.display = 'block';
    }

    switchToRichView() {
        this.richViewBtn.classList.add('active');
        this.jsonViewBtn.classList.remove('active');
        this.richView.classList.add('active');
        this.jsonView.classList.remove('active');
    }

    switchToJsonView() {
        this.jsonViewBtn.classList.add('active');
        this.richViewBtn.classList.remove('active');
        this.jsonView.classList.add('active');
        this.richView.classList.remove('active');
    }

    showRichResults() {
        const richOutput = document.getElementById('richOutput');
        if (!richOutput || !this.results) return;

        let html = '';

        if (Array.isArray(this.results)) {
            // Calculate summary statistics
            const totalPeople = this.results.length;
            const totalDocuments = this.results.reduce((sum, group) => sum + group.documents.length, 0);
            const documentTypes = new Set();
            this.results.forEach(group => {
                group.documents.forEach(doc => {
                    if (doc.DOCUMENT_TYPE) documentTypes.add(doc.DOCUMENT_TYPE);
                });
            });

            // Summary stats
            html += `
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-number">${totalPeople}</span>
                        <div class="stat-label">People Identified</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${totalDocuments}</span>
                        <div class="stat-label">Documents Processed</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${documentTypes.size}</span>
                        <div class="stat-label">Document Types</div>
                    </div>
                </div>
            `;

            // Person cards
            this.results.forEach((group, index) => {
                const personName = group.person_name || group.name || `Person ${index + 1}`;
                const initials = personName.split(' ').map(n => n[0]).join('').toUpperCase();

                html += `
                    <div class="person-card">
                        <div class="person-header">
                            <div class="person-avatar">${initials}</div>
                            <div class="person-info">
                                <h3>${personName}</h3>
                                <div class="document-count">${group.documents.length} document(s)</div>
                            </div>
                        </div>
                        <div class="documents-grid">
                `;

                group.documents.forEach(doc => {
                    const docType = doc.DOCUMENT_TYPE || 'UNKNOWN';
                    const displayType = docType === 'UNKNOWN' ? 'Unknown Document' :
                                       docType === '' ? 'Unknown Document' : docType;
                    const docIcon = this.getDocumentIcon(docType);

                    html += `
                        <div class="document-item">
                            <div class="document-header">
                                <span class="document-icon">${docIcon}</span>
                                <span class="document-title">${displayType}</span>
                            </div>
                            <div class="document-fields">
                    `;

                    // Display relevant fields
                    const fieldsToShow = [
                        { key: 'DOCUMENT_NUMBER', label: 'Document Number' },
                        { key: 'FIRST_NAME', label: 'First Name' },
                        { key: 'MIDDLE_NAME', label: 'Middle Name' },
                        { key: 'LAST_NAME', label: 'Last Name' },
                        { key: 'DATE_OF_BIRTH', label: 'Date of Birth' },
                        { key: 'GENDER', label: 'Gender' },
                        { key: 'ADDRESS', label: 'Address' }
                    ];

                    fieldsToShow.forEach(field => {
                        if (doc[field.key] && doc[field.key].trim()) {
                            html += `
                                <div class="field-row">
                                    <span class="field-label">${field.label}</span>
                                    <span class="field-value">${doc[field.key]}</span>
                                </div>
                            `;
                        }
                    });

                    html += `
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            });
        } else {
            html = '<div class="no-data">No grouped data available</div>';
        }

        richOutput.innerHTML = html;
    }

    getDocumentIcon(docType) {
        const icons = {
            'AADHAAR': '',
            'PAN': '',
            'PASSPORT': '📘',
            'DRIVING_LICENSE': '',
            'VOTER_ID': '',
            'GST_CERTIFICATE': '',
            'GST': '',
            'UNKNOWN': '',
            'MOCK': '',
            'default': ''
        };
        return icons[docType] || icons.default;
    }

    showJsonResults() {
        const jsonOutput = document.getElementById('jsonOutput');
        if (!jsonOutput) return;
        jsonOutput.textContent = JSON.stringify(this.results, null, 2);
    }

    async copyJson() {
        const jsonText = JSON.stringify(this.results, null, 2);
        try {
            await navigator.clipboard.writeText(jsonText);
            this.showTemporaryMessage(this.copyJsonBtn, 'Copied!');
        } catch (err) {
            console.error('Failed to copy: ', err);
        }
    }

    formatJson() {
        const jsonOutput = document.getElementById('jsonOutput');
        try {
            const parsed = JSON.parse(jsonOutput.textContent);
            jsonOutput.textContent = JSON.stringify(parsed, null, 4);
            this.showTemporaryMessage(this.formatJsonBtn, 'Formatted!');
        } catch (err) {
            console.error('Invalid JSON: ', err);
        }
    }

    showTemporaryMessage(button, message) {
        const originalText = button.textContent;
        button.textContent = message;
        setTimeout(() => {
            button.textContent = originalText;
        }, 2000);
    }

    downloadResults() {
        if (!this.results) return;

        const dataStr = JSON.stringify(this.results, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `extraction-results-${new Date().toISOString().slice(0,10)}.json`;
        link.click();
    }

    exportRichData() {
        if (!this.results) return;

        // Create a formatted text summary
        let summary = 'DOCUMENT EXTRACTION SUMMARY\n';
        summary += '=' .repeat(50) + '\n\n';
        summary += `Generated: ${new Date().toLocaleString()}\n\n`;

        if (Array.isArray(this.results)) {
            const totalPeople = this.results.length;
            const totalDocuments = this.results.reduce((sum, group) => sum + group.documents.length, 0);

            summary += `Total People: ${totalPeople}\n`;
            summary += `Total Documents: ${totalDocuments}\n\n`;

            this.results.forEach((group, index) => {
                const personName = group.person_name || group.name || `Person ${index + 1}`;
                summary += `${index + 1}. ${personName}\n`;
                summary += '-'.repeat(personName.length + 4) + '\n';

                group.documents.forEach((doc, docIndex) => {
                    summary += `   Document ${docIndex + 1}: ${doc.DOCUMENT_TYPE || 'Unknown'}\n`;
                    if (doc.DOCUMENT_NUMBER) summary += `   Number: ${doc.DOCUMENT_NUMBER}\n`;
                    if (doc.DATE_OF_BIRTH) summary += `   DOB: ${doc.DATE_OF_BIRTH}\n`;
                    if (doc.ADDRESS) summary += `   Address: ${doc.ADDRESS}\n`;
                    summary += '\n';
                });
                summary += '\n';
            });
        }

        const dataBlob = new Blob([summary], {type: 'text/plain'});
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `extraction-summary-${new Date().toISOString().slice(0,10)}.txt`;
        link.click();

        this.showTemporaryMessage(this.exportRichBtn, 'Exported!');
    }

    printRichView() {
        const richContent = document.getElementById('richOutput').innerHTML;
        const printWindow = window.open('', '_blank');

        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Document Extraction Results</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .summary-stats { background: #f0f0f0; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                    .person-card { border: 1px solid #ddd; margin-bottom: 20px; padding: 15px; border-radius: 8px; }
                    .person-header { border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 15px; }
                    .documents-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
                    .document-item { background: #f9f9f9; padding: 10px; border-radius: 5px; }
                    .field-row { display: flex; justify-content: space-between; padding: 3px 0; }
                    .field-label { font-weight: bold; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <h1>Document Extraction Results</h1>
                <p>Generated: ${new Date().toLocaleString()}</p>
                ${richContent}
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();

        this.showTemporaryMessage(this.printRichBtn, 'Printing...');
    }

    showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        this.errorSection.style.display = 'block';
    }

    hideError() {
        this.errorSection.style.display = 'none';
    }

    hideResults() {
        this.resultsSection.style.display = 'none';
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new DocumentExtractor();
});
