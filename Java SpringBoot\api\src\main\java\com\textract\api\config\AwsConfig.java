package com.textract.api.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.textract.TextractClient;

/**
 * AWS Configuration for Textract and S3 clients
 */
@Configuration
public class AwsConfig {

    private static final Logger logger = LoggerFactory.getLogger(AwsConfig.class);

    @Autowired
    private ApplicationProperties applicationProperties;

    @Bean
    public TextractClient textractClient() {
        try {
            ApplicationProperties.Aws awsConfig = applicationProperties.getAws();
            
            if (awsConfig.getAccessKeyId() != null && awsConfig.getSecretAccessKey() != null) {
                AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(
                    awsConfig.getAccessKeyId(), 
                    awsConfig.getSecretAccessKey()
                );

                Region awsRegion = Region.of(awsConfig.getRegion());

                TextractClient client = TextractClient.builder()
                    .region(awsRegion)
                    .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                    .build();

                logger.info("SUCCESS: AWS Textract client initialized successfully");
                return client;
            } else {
                logger.warn("WARNING: AWS credentials not found in configuration");
                return null;
            }
        } catch (Exception e) {
            logger.error("ERROR: Failed to initialize AWS Textract client: {}", e.getMessage());
            return null;
        }
    }

    @Bean
    public S3Client s3Client() {
        try {
            ApplicationProperties.Aws awsConfig = applicationProperties.getAws();
            
            if (awsConfig.getAccessKeyId() != null && awsConfig.getSecretAccessKey() != null) {
                AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(
                    awsConfig.getAccessKeyId(), 
                    awsConfig.getSecretAccessKey()
                );

                Region awsRegion = Region.of(awsConfig.getRegion());

                S3Client client = S3Client.builder()
                    .region(awsRegion)
                    .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                    .build();

                logger.info("SUCCESS: AWS S3 client initialized successfully");
                logger.info("SUCCESS: Using S3 bucket: {} in region: {}", 
                    awsConfig.getS3().getBucketName(), awsConfig.getRegion());

                // Test S3 connection
                testS3Connection(client, awsConfig.getS3().getBucketName());
                
                return client;
            } else {
                logger.warn("WARNING: AWS credentials not found in configuration");
                return null;
            }
        } catch (Exception e) {
            logger.error("ERROR: Failed to initialize AWS S3 client: {}", e.getMessage());
            return null;
        }
    }

    private void testS3Connection(S3Client s3Client, String bucketName) {
        try {
            s3Client.headBucket(builder -> builder.bucket(bucketName));
            logger.info("SUCCESS: S3 bucket '{}' is accessible", bucketName);
        } catch (Exception e) {
            logger.warn("WARNING: S3 bucket '{}' may not exist or is not accessible: {}", bucketName, e.getMessage());
            logger.warn("WARNING: Files will still be processed, but S3 upload may fail");
        }
    }
}
