package com.textract.ui.service;

import com.textract.ui.config.UIProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.util.List;

/**
 * Service for proxying requests to the API server
 */
@Service
public class ApiProxyService {

    private static final Logger logger = LoggerFactory.getLogger(ApiProxyService.class);

    @Autowired
    private WebClient webClient;

    @Autowired
    private UIProperties uiProperties;

    /**
     * Proxy file upload to API server
     */
    public Mono<String> proxyUpload(List<MultipartFile> files) {
        logger.info("Proxying upload request to API server for {} files", files.size());

        try {
            MultipartBodyBuilder builder = new MultipartBodyBuilder();
            
            for (MultipartFile file : files) {
                builder.part("files", new ByteArrayResource(file.getBytes()) {
                    @Override
                    public String getFilename() {
                        return file.getOriginalFilename();
                    }
                }).header("Content-Disposition", 
                    "form-data; name=\"files\"; filename=\"" + file.getOriginalFilename() + "\"");
            }

            return webClient.post()
                    .uri(uiProperties.getApi().getBaseUrl() + "/upload")
                    .contentType(MediaType.MULTIPART_FORM_DATA)
                    .body(BodyInserters.fromMultipartData(builder.build()))
                    .retrieve()
                    .bodyToMono(String.class)
                    .doOnSuccess(response -> logger.info("Upload proxy successful"))
                    .doOnError(error -> logger.error("Upload proxy failed: {}", error.getMessage()));

        } catch (IOException e) {
            logger.error("Error preparing upload proxy: {}", e.getMessage());
            return Mono.error(new RuntimeException("Error preparing upload proxy: " + e.getMessage()));
        }
    }

    /**
     * Proxy GET request to API server
     */
    public Mono<String> proxyGet(String path) {
        logger.debug("Proxying GET request: {}", path);
        
        return webClient.get()
                .uri(uiProperties.getApi().getBaseUrl() + path)
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> logger.debug("GET proxy successful for: {}", path))
                .doOnError(error -> logger.error("GET proxy failed for {}: {}", path, error.getMessage()));
    }

    /**
     * Proxy POST request to API server
     */
    public Mono<String> proxyPost(String path, Object body) {
        logger.debug("Proxying POST request: {}", path);
        
        return webClient.post()
                .uri(uiProperties.getApi().getBaseUrl() + path)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(body != null ? body : "")
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> logger.debug("POST proxy successful for: {}", path))
                .doOnError(error -> logger.error("POST proxy failed for {}: {}", path, error.getMessage()));
    }
}
