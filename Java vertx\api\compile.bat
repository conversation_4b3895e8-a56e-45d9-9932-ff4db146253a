@echo off
echo Compiling TextractService.java...

REM Find Java installation
for /f "tokens=*" %%i in ('where java') do set JAVA_PATH=%%i
echo Java found at: %JAVA_PATH%

REM Get Java home directory
for %%i in ("%JAVA_PATH%") do set JAVA_HOME=%%~dpi..
echo Java home: %JAVA_HOME%

REM Set classpath
set CLASSPATH=target\classes;target\dependency\*

REM Compile the Java file
"%JAVA_HOME%\bin\javac.exe" -cp "%CLASSPATH%" -d target\classes src\main\java\com\textract\api\TextractService.java

if %ERRORLEVEL% EQU 0 (
    echo Compilation successful!
) else (
    echo Compilation failed with error code %ERRORLEVEL%
)

pause
