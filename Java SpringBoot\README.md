# AWS Textract Document Extraction - Spring Boot

A complete Spring Boot implementation of the AWS Textract document extraction system with separate API and UI microservices.

## 🏗️ Architecture

This project consists of two separate Spring Boot applications:

- **API Server** (`api/`) - Document processing service with AWS Textract integration
- **UI Server** (`ui/`) - Web interface and static file server with API proxy

## 🚀 Quick Start

### Prerequisites

- Java 11 or higher
- Maven 3.6+
- AWS credentials configured (optional - system will indicate if unavailable)

### Option 1: Start Both Services (Recommended)

```bash
# Start both API and UI servers
start-both.bat
```

### Option 2: Start Services Individually

#### 1. Start the API Server

```bash
cd api
mvn spring-boot:run
```

The API server will start on `http://localhost:3333`

#### 2. Start the UI Server

```bash
cd ui
mvn spring-boot:run
```

The UI server will start on `http://localhost:8081`

### 3. Access the Application

- **Web Interface**: http://localhost:8081
- **API Documentation**: http://localhost:8081/api-docs.html
- **API Health Check**: http://localhost:3333/health
- **UI Health Check**: http://localhost:8081/ui/health

## 📁 Project Structure

```
Java SpringBoot/
├── api/                                    # API Server
│   ├── src/main/java/com/textract/api/
│   │   ├── DocumentExtractionApiApplication.java  # Main Spring Boot app
│   │   ├── controller/
│   │   │   └── DocumentController.java     # REST controllers
│   │   ├── service/
│   │   │   ├── DocumentProcessor.java      # Document processing logic
│   │   │   └── TextractService.java        # AWS Textract integration
│   │   └── config/
│   │       ├── ApplicationProperties.java  # Configuration properties
│   │       ├── AwsConfig.java             # AWS client configuration
│   │       └── WebConfig.java             # Web/CORS configuration
│   ├── src/main/resources/
│   │   └── application.yml                # Spring Boot configuration
│   └── pom.xml                           # Maven dependencies
├── ui/                                   # UI Server
│   ├── src/main/java/com/textract/ui/
│   │   ├── DocumentExtractionUIApplication.java  # Main Spring Boot app
│   │   ├── controller/
│   │   │   └── UIController.java          # UI and proxy controllers
│   │   ├── service/
│   │   │   └── ApiProxyService.java       # API proxy service
│   │   └── config/
│   │       ├── UIProperties.java          # UI configuration properties
│   │       └── WebConfig.java             # Web configuration
│   ├── src/main/resources/
│   │   ├── application.yml               # Spring Boot configuration
│   │   └── static/                       # Static web files
│   │       ├── index.html                # Main web interface
│   │       ├── script.js                 # Frontend JavaScript
│   │       ├── styles.css                # CSS styles
│   │       └── api-docs.html             # API documentation
│   └── pom.xml                          # Maven dependencies
├── start-api.bat                        # Start API server
├── start-ui.bat                         # Start UI server
├── start-both.bat                       # Start both servers
└── README.md                            # This file
```

## 🔧 Configuration

### API Server Configuration (`api/src/main/resources/application.yml`)

```yaml
server:
  port: 3333
  address: 0.0.0.0

aws:
  access-key-id: YOUR_ACCESS_KEY
  secret-access-key: YOUR_SECRET_KEY
  region: ap-south-1
  s3:
    bucket-name: doc-scan-textract
  textract:
    enabled: true
    timeout: 30000

upload:
  max-file-size: 52428800
  allowed-extensions:
    - jpg
    - jpeg
    - png
    - pdf
```

### UI Server Configuration (`ui/src/main/resources/application.yml`)

```yaml
server:
  port: 8081
  address: 0.0.0.0

api:
  host: localhost
  port: 3333
  base-url: http://localhost:3333
```

## 🛠️ Building and Running

### Build JAR Files

```bash
# Build API server
cd api
mvn clean package

# Build UI server
cd ui
mvn clean package
```

### Run JAR Files

```bash
# Run API server
java -jar api/target/document-extraction-api-springboot-1.0.0.jar

# Run UI server
java -jar ui/target/document-extraction-ui-springboot-1.0.0.jar
```

## 📋 API Endpoints

### API Server (Port 3333)

- `POST /upload` - Upload and process documents
- `GET /api/grouped-by-person` - Get processed documents grouped by person
- `POST /api/process-personal-documents` - Process personal documents
- `GET /health` - Health check
- `GET /aws-status` - AWS service status
- `GET /api/info` - API information

### UI Server (Port 8081)

- `GET /` - Main web interface
- `GET /api-docs.html` - API documentation
- `GET /ui/health` - UI server health check
- `GET /ui/config` - UI configuration
- Proxy routes to API server for all `/api/*` and `/upload` requests

## 🔍 Features

### Document Processing
- **AWS Textract Integration** - Real OCR processing with AWS Textract
- **Multiple Document Types** - AADHAAR, PAN, Passport, Driving License, etc.
- **Pattern-Based Extraction** - Fallback text extraction using regex patterns
- **Field Mapping** - Automatic mapping to standard field formats
- **S3 Integration** - Automatic file upload to S3 for PDF processing

### Web Interface
- **Drag & Drop Upload** - Easy file upload interface
- **Multiple File Support** - Process multiple documents at once
- **Rich View** - Formatted display of extracted data
- **JSON View** - Raw JSON output with formatting
- **Export Options** - Download results in various formats
- **Responsive Design** - Works on desktop and mobile

### Spring Boot Features
- **Auto-configuration** - Automatic configuration of components
- **Actuator Endpoints** - Built-in health checks and metrics
- **Configuration Properties** - Type-safe configuration binding
- **Reactive Web Client** - Non-blocking API proxy
- **Static Resource Serving** - Efficient static file serving

## 🔒 Security

- **CORS Configuration** - Proper cross-origin resource sharing setup
- **Input Validation** - File type and size validation
- **Error Handling** - Comprehensive error handling and logging
- **Spring Security Ready** - Easy to add authentication/authorization

## 🧪 Testing

### Test File Upload

```bash
curl -X POST http://localhost:3333/upload \
  -F "files=@document.jpg"
```

### Test API Endpoints

```bash
# Health check
curl http://localhost:3333/health

# AWS status
curl http://localhost:3333/aws-status

# Grouped documents
curl http://localhost:3333/api/grouped-by-person
```

## 📊 Monitoring

### Health Checks

- API Server: `GET http://localhost:3333/health`
- UI Server: `GET http://localhost:8081/ui/health`
- Spring Boot Actuator: `GET http://localhost:3333/actuator/health`

### Logs

- API logs: `logs/api.log`
- UI logs: `logs/ui.log`
- Console output with structured logging

## 🔄 Development

### Development Mode

```bash
# API server with hot reload
cd api
mvn spring-boot:run

# UI server with hot reload
cd ui
mvn spring-boot:run
```

### Development Tools

- **Spring Boot DevTools** - Automatic restart on code changes
- **Actuator** - Development and production monitoring
- **Configuration Processor** - IDE support for configuration properties

## 🚀 Deployment

### Production Configuration

```yaml
# application-prod.yml
logging:
  level:
    root: WARN
    com.textract: INFO
  file:
    name: /var/log/app.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
```

### Docker Deployment

```dockerfile
FROM openjdk:11-jre-slim
COPY target/*.jar app.jar
EXPOSE 3333
CMD ["java", "-jar", "app.jar"]
```

## 🤝 Comparison with Vert.x Version

This Spring Boot implementation provides:

- **Convention over Configuration** - Less boilerplate code
- **Rich Ecosystem** - Extensive Spring ecosystem integration
- **Auto-configuration** - Automatic component configuration
- **Production Ready** - Built-in monitoring and management features
- **Developer Experience** - Better IDE support and debugging
- **Enterprise Integration** - Easy integration with Spring ecosystem

## 🔄 Migration from Vert.x

This Spring Boot version was converted from the original Java Vert.x implementation with the following key changes:

### Architecture Changes
- **Vert.x Verticles** → **Spring Boot Applications** with `@SpringBootApplication`
- **Vert.x Router** → **Spring MVC Controllers** with `@RestController`
- **Vert.x Config** → **Spring Boot Configuration Properties** with `@ConfigurationProperties`
- **Vert.x Web Client** → **Spring WebFlux WebClient** for reactive HTTP calls

### Key Conversions
- `MainVerticle.java` → `DocumentExtractionApiApplication.java` + Controllers + Services
- `UIVerticle.java` → `DocumentExtractionUIApplication.java` + Controllers + Services
- JSON configuration → YAML configuration with type-safe properties
- Manual dependency injection → Spring's `@Autowired` and `@Service`

### Benefits of Spring Boot Version
- **Reduced Boilerplate** - Less configuration and setup code
- **Better IDE Support** - Enhanced autocomplete and debugging
- **Rich Ecosystem** - Access to Spring ecosystem (Security, Data, etc.)
- **Production Features** - Built-in monitoring, health checks, and metrics
- **Convention over Configuration** - Sensible defaults reduce configuration

## 📝 License

This project is part of the AWS Textract Document Extraction system.
